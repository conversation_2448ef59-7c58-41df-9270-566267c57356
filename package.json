{"name": "starter", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"framer-motion": "^10.16.4", "react": "18.2.0", "react-dom": "18.2.0", "sass": "^1.68.0"}, "devDependencies": {"@types/react": "18.2.15", "@types/react-dom": "18.2.7", "@vitejs/plugin-react": "4.0.3", "eslint": "8.45.0", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.3", "vite": "4.4.5"}}