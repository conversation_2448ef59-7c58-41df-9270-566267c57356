.services{
    background: linear-gradient(180deg, #0c0c1d, #111132);  
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;

    .textContainer{
        flex: 1;
        align-self: flex-end;
        display: flex;
        align-items: center;
        gap: 20px;

        p{
            font-weight: 200;
            font-size: 20px;
            color: gray;
            text-align: right;
        }
    }
    hr{
        width: 500px;
        border: none;
        border-top: 0.5px solid gray;
    }

    .titleContainer{
        flex: 2;
        display: flex;
        flex-direction: column;
        align-items: center;



        .title{
            display: flex;
            gap: 50px;
            img{
                width: 300px;
                height: 100px;
                border-radius: 50px;
                object-fit: cover;
            }

            h1{
                font-size: 96px;
                font-weight: 100;
            }

            button{
                width: 300px;
                height: 100px;
                border-radius: 50px;
                background-color: orange;
                border: none;
                font-size: 24px;cursor: pointer;
            }
        }
    }

    .listContainer{
        flex: 2;
        display: flex;
        max-width: 1366px;
        margin: auto;

        .box{
            padding: 50px;
            border: 0.5px solid gray;
            display: flex;
            flex-direction: column;
            justify-content: space-between;


            button{
                padding: 10px;
                background-color: orange;
                border: navajowhite;
                cursor: pointer;
            }
        }
    }
}