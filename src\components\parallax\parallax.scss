.parallax{
    width: 100%;
    height: 100%;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;


    h1{
        font-size: 100px;
    }


    .mountains{
        background-image: url("/mountains.png");
        background-size: cover;
        background-position: bottom;
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 3;
    }

    .planets{
        background-image: url("/planets.png");
        background-size: cover;
        background-position: bottom;
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 2;
    }

    .stars{
        background-image: url("/stars.png");
        background-size: cover;
        background-position: bottom;
        width: 100%;
        height: 100%;
        position: absolute;
        z-index: 1;
    }

}