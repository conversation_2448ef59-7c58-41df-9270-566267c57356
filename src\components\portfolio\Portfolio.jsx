import { useRef } from "react";
import "./portfolio.scss";
import { motion, useScroll, useSpring, useTransform } from "framer-motion";

const items = [
  {
    id: 1,
    title: "React Crypto App ",
    img: "https://images.pexels.com/photos/166126/pexels-photo-166126.jpeg?auto=compress&cs=tinysrgb&w=600",
    desc: "This is a responsive and interactive cryptocurrency application built using ReactJS. The app fetches real-time data from a crypto API and displays key market statistics such as current prices, market caps, 24-hour changes, and coin rankings. Users can search for specific cryptocurrencies, view detailed information, and track performance trends",
  },
  {
    id: 2,
    title: "Netflix Clone",
    img: "https://images.pexels.com/photos/31145166/pexels-photo-31145166.jpeg?auto=compress&cs=tinysrgb&w=600&lazy=load",
    desc: "This project is a Netflix-inspired video streaming UI clone developed using ReactJS. It replicates the core design and layout of the official Netflix platform, featuring a homepage with banners, rows of categorized movies, and a dynamic UI. The app fetches data using an API (such as TMDB) to display trending, popular, and upcoming movies and shows",
  },
  {
    id: 3,
    title: "Vanilla JS Commerce",
    img: "https://images.pexels.com/photos/29803747/pexels-photo-29803747/free-photo-of-scenic-view-of-old-port-and-notre-dame-de-la-garde.jpeg?auto=compress&cs=tinysrgb&w=600&lazy=load",
    desc: "This is a fully functional E-commerce store built using Vanilla JavaScript, HTML, and CSS—without any frameworks or libraries. The store features a clean, responsive UI with product listings, categories, a shopping cart, and dynamic total price updates. Users can add or remove items from the cart, view product details, and simulate a checkout experience.",
  },
  {
    id: 4,
    title: "Youtube Clone",
    img: "https://images.pexels.com/photos/8563018/pexels-photo-8563018.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1",
    desc: "This is a YouTube-inspired video streaming application developed using React.js. The project replicates core features of the YouTube platform, including a homepage with trending videos, video detail pages, search functionality, and a responsive video player. It uses React functional components, React Router for navigation, and API integration (e.g., YouTube Data API or mock data) to fetch and display dynamic video content.",
  },
];

const Single = ({ item }) => {
  const ref = useRef();

  const { scrollYProgress } = useScroll({
    target: ref,
    // offset: ["start start", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [-300, 300]);
  return (
    <section>
      <div className="container">
        <div className="wrapper">
            <div className="imageContainer" ref={ref}>
                <img src={item.img} alt="" />
            </div>
        <motion.div className="textContainer" style={{y}}>
          <h2>{item.title}</h2>
          <p>{item.desc}</p>
          <button>See Demo</button>
        </motion.div>
        </div>
      </div>
    </section>
  );
};

const Portfolio = () => {
  const ref = useRef();

  const { scrollYProgress } = useScroll();

  const scaleX = useSpring(scrollYProgress, {
    stiffness: 100,
    damping: 30,
  });

  return (
    <div className="portfolio" ref={ref}>
      <div className="progress">
        <h1>Featured Works</h1>
        <motion.div style={{ scaleX }} className="progressBar"></motion.div>
      </div>
      {items.map((item) => (
        <Single item={item} key={item.id} />
      ))}
    </div>
  );
};

export default Portfolio;
