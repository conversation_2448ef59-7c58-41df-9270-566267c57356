.hero{
    height: calc(100vh - 100px);
    background: linear-gradient(180deg, #0c0c1d, #111132);
    overflow: hidden;
    position: relative;
    z-index: -1;




    .wrapper{
        max-width: 1366px;
        height: 100%;
        margin: auto;


        .textContainer{
            height: 100%;
            width: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 40px;


            h2{
                font-size: 30px;
                color: rebeccapurple;
                letter-spacing: 10px;
            }

            
            h1{
                font-size: 88px;
            }


            .buttons{
                button{
                    padding: 20px;
                    border: 1px solid white;
                    border-radius: 10px;
                    background-color: transparent;
                    color: white;
                    margin-right: 20px;
                    font-weight: 300;
                    cursor: pointer;
                }
            }


            img{
                width: 50px;

            }
        }
    }

    .imageContainer{
        height: 100%;
        position: absolute;
        top: 0;
        right: 0;
    }



    .slidingTextContainer{
        position: absolute;
        font-size: 50vh;
        bottom: -120px;
        white-space: nowrap;
        color: #ffffff09;
        width: 50%;
        font-weight: bold;
    }
}