import { motion } from 'framer-motion';
import { useState } from 'react';

const Test = () => {
  const [open, setOpen] = useState(false);

  const listVariants = {
    visible: {
      transition: {
        staggerChildren: 0.2
      }
    },
    hidden: {}
  };

  const itemVariants = {
    visible: (i) => ({
      opacity: 1,
      x: 100,
      transition: {
        delay: i * 0.3
      }
    }),
    hidden: {
      opacity: 0,
      x: 0
    }
  };

  const items = ["item1", "item2", "item3", "item4"];

  return (
    <div className="course">
      <motion.ul initial="hidden" animate="visible" variants={listVariants}>
        {items.map((item, i) => (
          <motion.li key={item} variants={itemVariants} custom={i}>
            {item}
          </motion.li>
        ))}
      </motion.ul>
    </div>
  );
};

export default Test;
                                                                                            