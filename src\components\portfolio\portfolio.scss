.portfolio{
    position: relative;

    .progress{
        position: sticky;
        top: 0;
        left: 0;
        padding-top: 50px;
        text-align: center;
        color: orange;
        font-size: 36px;
        h1{

        }

        .progressBar{
            height: 10px;
            background-color: #fff;
        }
    }

    .container{
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        height: 100%;
        overflow: hidden;

        .wrapper{
            min-width: 1366px;
            height: 100%;
            margin: auto;
            display: flex;
            gap: 50px;
            justify-content: center;
            align-items: center;
            
            .imageContainer{
                flex: 1s;
                height: 50%;

                img{
                    width: 100%;
                    height: 90%;
                    object-fit: cover;
                }
            }
            
            .textContainer{
                flex: 1;
                display: flex;
                flex-direction: column;
                gap: 30px;
            }

            h2{
                font-size: 72px;
            }

            p{
                color: gray;
                font-size: 20px;
            }

            button{
                background-color: orange;
                border: none;
                border-radius: 10px;
                padding: 10px;
                width: 200px;
                cursor: pointer;
            }
        }
    }
}